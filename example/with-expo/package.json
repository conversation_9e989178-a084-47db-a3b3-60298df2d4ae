{"name": "with-expo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"expo": "~50.0.8", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.4", "expo-location": "~16.5.5", "expo-splash-screen": "~0.26.4", "react-native-wifi-reborn": "file:../.."}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3"}, "private": true}