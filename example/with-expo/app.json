{"expo": {"name": "with-expo", "slug": "with-expo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.rnwifi.with-expo"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.ACCESS_FINE_LOCATION"], "package": "com.rnwifi.withexpo"}, "plugins": ["react-native-wifi-reborn"]}}