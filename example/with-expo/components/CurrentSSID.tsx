import { useEffect, useState } from 'react';
import { Button, Text, Platform } from 'react-native';
import WifiManager from 'react-native-wifi-reborn';
import { Section } from './Section';

export const CurrentSSID = () => {
  const [ssid, setSsid] = useState('fetching...');

  const handleFetch = async () => {
    setSsid('fetching...');

    if (Platform.OS === 'web') {
      setSsid('WiFi not available on web');
      return;
    }

    try {
      console.log('Attempting to get WiFi SSID...');
      const currentSSID = await WifiManager.getCurrentWifiSSID();
      console.log('WiFi SSID result:', currentSSID);
      setSsid(currentSSID || 'No WiFi connected');
    } catch (error) {
      console.log('WiFi Error:', error);
      setSsid(`Error: ${error.message || 'WiFi not available'}`);
    }
  };

  useEffect(() => {
    handleFetch();
  }, []);

  return (
    <Section title="Current SSID">
      <Text>{ssid}</Text>
      <Button title="Refresh" onPress={handleFetch} />
    </Section>
  );
};
