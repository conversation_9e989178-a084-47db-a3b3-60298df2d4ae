import { useState } from 'react';
import { Button, TextInput, StyleSheet, Text, ActivityIndicator } from 'react-native';
import WifiManager from 'react-native-wifi-reborn';
import { Section } from './Section';

export const ConnectToSSID = () => {
  const [ssid, setSsid] = useState('SSID');
  const [pass, setPass] = useState('password');
  const [error, setError] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleConnect = async () => {
    setError('');
    setResponse('');
    setIsLoading(true);

    try {
      const result = await WifiManager.connectToProtectedWifiSSID({
        ssid: ssid,
        password: pass,
        isWEP: false,
        isHidden: false,
        timeout: 10,
      });
      setResponse('Connected successfully!');
      console.log('WiFi connection result:', result);
    } catch (error) {
      console.log('WiFi connection error:', error);
      setError(`Connection failed: ${error.message || error.toString()}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Section title="Connect to SSID">
      <TextInput style={styles.textInput} value={ssid} onChangeText={setSsid} />
      <TextInput style={styles.textInput} value={pass} onChangeText={setPass} />
      <Button title="Connect" onPress={handleConnect} disabled={isLoading} />
      {isLoading ? (
        <ActivityIndicator />
      ) : (
        <>
          <Text style={styles.error}>{error}</Text>
          <Text>{response}</Text>
        </>
      )}
    </Section>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 12,
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 10,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e5e5ea',
    padding: 4,
    borderRadius: 4,
  },
  error: {
    color: 'red',
  },
});
