name: "Close stale issues"
on:
  schedule:
  - cron: "0 0 */7 * *"

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/stale@v3
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'Hey there, it looks like there has been no activity on this issue recently. Has the issue been fixed, or does it still require the community attention? This issue may be closed if no further activity occurs.'
        stale-issue-label: 'stale'
        exempt-issue-labels: 'work-in-progress'
        days-before-stale: 30
        days-before-close: 5
