def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.3'
    }
}

apply plugin: 'com.android.library'

android {
    namespace = "com.reactlibrary.rnwifi"

    compileSdkVersion safeExtGet(compileSdkVersion, 31)

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion safeExtGet(minSdkVersion, 21)
        targetSdkVersion safeExtGet(targetSdkVersion, 31)
        versionCode 1
        versionName '1.0'
    }
}

repositories {
    maven {
        url "$projectDir/../node_modules/react-native/android"
    }
    google()
    mavenCentral()
}

dependencies {
    //noinspection GradleDynamicVersion
    implementation 'com.facebook.react:react-native:+'
    implementation 'io.github.thanosfisherman.wifiutils:wifiutils:1.6.6'
}
