{"name": "react-native-wifi-reborn", "version": "4.13.6", "description": "A react-native implementation for viewing and connecting to Wifi networks on Android and iOS devices.", "types": "lib/types/index.d.ts", "main": "src/index.js", "scripts": {"lint": "eslint src/ lib/ plugin/ --ext .js,.jsx,.ts,.tsx", "test": "echo \"No test specified\" && exit 0", "build:plugin": "tsc --build plugin", "checkjs": "tsc", "semantic-release": "semantic-release"}, "files": ["app.plugin.js", "/android/src/", "/android/build.gradle", "/ios", "!Podfile*", "/windows", "/src", "/lib", "/plugin", "/*.podspec", "/jest"], "keywords": ["react", "react-native", "android", "ios", "wifi", "connect"], "author": "", "repository": {"type": "git", "url": "git+https://github.com/JuanSeBestia/react-native-wifi-reborn.git"}, "peerDependencies": {"react-native": ">=0.13"}, "dependencies": {"expo": "^53.0.19"}, "bugs": {"url": "https://github.com/JuanSeBestia/react-native-wifi-reborn/issues"}, "homepage": "https://github.com/JuanSeBestia/react-native-wifi-reborn#readme", "license": "ISC", "devDependencies": {"@babel/core": "^7.8.3", "@expo/config-plugins": "^4.0.7", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/react-native": "^0.61.17", "@typescript-eslint/eslint-plugin": "^2.21.0", "@typescript-eslint/parser": "^2.21.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "prettier": "^1.19.1", "react": "^16.9.0", "react-native": "^0.61.5", "semantic-release": "^23.0.2", "typescript": "^3.8.2"}}