Nodeunit contributors (sorted alphabetically)
============================================

* **[mikeislearning](https://github.com/mikeislearning)**

  * Change settingsURL

* **[eliaslecomte](https://github.com/eliaslecomte)**

  * Update gradle configuration
  * Update Android library
  * Use [WifiUtils](https://github.com/ThanosFisherman/WifiUtils/)
  * Use promises over callbacks
  * Return meaningful exceptions

* **[kristfal](https://github.com/kristfal)**

  * [android] Rename the default name of package

* **[Rapsssito](https://github.com/Rapsssito)**

  * [android] Added network compatibility

* **[gabrielrra](https://github.com/gabrielrra)**

  * [docs] Update docs

* **[thibmaek](https://github.com/thibmaek)**

  * [docs] Added type declarations
